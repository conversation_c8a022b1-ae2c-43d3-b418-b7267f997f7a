syntax = "proto3";

package editor;

service EditorService {
  rpc CreateManyPages(CreateManyPagesRequest) returns (CreateManyPagesResponse);
  rpc RemoveManyPages(RemoveManyPagesRequest) returns (RemoveManyPagesResponse);
}

message CreatePageRequest {
  string serviceId = 1;
  string entityId = 2;
  string schoolId = 3;
}

message CreatePageResponse {
  string pageId = 1;
  string serviceId = 2;
  string entityId = 3;
  string schoolId = 4;
}

message CreateManyPagesRequest {
  repeated CreatePageRequest pages = 1;
}

message CreateManyPagesResponse {
  repeated CreatePageResponse pages = 1;
}

message RemoveManyPagesRequest {
  repeated string pageIds = 1;
}

message RemoveManyPagesResponse {
  bool success = 1;
  repeated string removedPageIds = 2;
}
